import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_form_cubit.dart';
import 'package:cp_associates/features/admin/presentation/widget/admin_task_form.dart';
import 'package:cp_associates/features/admin/presentation/widget/admintask_tile.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/home/<USER>/cubit/setting_cubit.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class AdminTasks extends StatefulWidget {
  const AdminTasks({super.key});

  @override
  State<AdminTasks> createState() => _AdminTasksState();
}

class _AdminTasksState extends State<AdminTasks> {
  @override
  void initState() {
    super.initState();
    final cubit = context.read<AdminTaskCubit>();
    // cubit.fetchAllAdminTask();
    cubit.filterTask(AdminTaskTypes.onGoing, context); // <- Add this
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdminTaskCubit, AdminTaskState>(
      builder: (context, state) {
        return ResponsiveWidCustom(
          mobile: Scaffold(
            appBar: AppBar(
              title: Text("Admin Tasks", style: AppTextStyles.appBarHeading),
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              // padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  filterTabs(context, state),
                  SizedBox(height: 20),
                  state.selectedType == AdminTaskTypes.completed
                      ? Row(
                        children: [
                          Expanded(
                            child: DropdownButtonHideUnderline(
                              child: DropdownButtonFormField<int>(
                                decoration: InputDecoration(
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: AppColors.grey2,
                                    ),
                                  ),
                                  hintText: "Select Month",
                                  hintStyle: AppTextStyles.hintText,

                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                value: state.selectedMonth,
                                onChanged: (value) {
                                  if (value != null) {
                                    context.read<AdminTaskCubit>().selectMonth(
                                      value,
                                    );

                                    context
                                                .read<AuthCubit>()
                                                .currentUser
                                                ?.role ==
                                            "admin"
                                        ? context
                                            .read<AdminTaskCubit>()
                                            .fetchCompletedAdminTask(
                                              DateTime(
                                                DateTime.now().year,
                                                value,
                                              ),
                                            )
                                        : context
                                            .read<AdminTaskCubit>()
                                            .fetchCompletedAdminTaskByCurrentUser(
                                              DateTime(
                                                DateTime.now().year,
                                                value,
                                              ),
                                              FBAuth.auth.currentUser?.uid ??
                                                  "",
                                            );
                                  }
                                },
                                items: List.generate(monthCount, (index) {
                                  // Wrap around year if needed
                                  final monthIndex =
                                      ((startMonth + index - 1) % 12) + 1;
                                  return DropdownMenuItem<int>(
                                    value: monthIndex,
                                    child: Text(monthMap[monthIndex] ?? ''),
                                  );
                                }),
                              ),
                            ),
                          ),
                        ],
                      )
                      : SizedBox(),
                  SizedBox(
                    height:
                        state.selectedType == AdminTaskTypes.completed ? 20 : 0,
                  ),
                  Expanded(
                    child: Stack(
                      children: [
                        AdminTaskTile(isMobile: true),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Container(
                              alignment: Alignment.bottomRight,
                              child: AddBtn(
                                text: "Add Task",

                                onPressed: () {
                                  showModalBottomSheet(
                                    isScrollControlled: true,
                                    context: context,
                                    builder: (context) {
                                      return Padding(
                                        padding:
                                            MediaQuery.of(context).viewInsets,
                                        child: BlocProvider(
                                          create:
                                              (context) => AdminTaskFormCubit(
                                                context
                                                    .read<AdminTaskCubit>()
                                                    .adminTaskRepo,
                                              ),
                                          child: AdminTaskForm(
                                            editAdminTask: null,
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                                color: AppColors.secondary,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 30),
                ],
              ),
            ),
          ),
          desktop: Scaffold(
            backgroundColor: AppColors.containerGreyColor,
            appBar: PreferredSize(
              child: CommonAppBar(),
              preferredSize: Size.fromHeight(60),
            ),
            body: GestureDetector(
              onTap: () {
                final settingCubit = context.read<SettingCubit>();
                if (settingCubit.controller.isShowing) {
                  settingCubit.controller.toggle();
                } else {
                  null;
                }
              },
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 160,
                    vertical: 50,
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          InkWell(
                            onTap: () {
                              context.go(Routes.home);
                            },
                            child: Icon(Icons.arrow_back),
                          ),
                          SizedBox(width: 30),
                          Text(
                            "Admin Tasks (${state.adminTasks.length})",
                            style: AppTextStyles.appBarHeading,
                          ),
                          Spacer(),
                          AddBtn(
                            text: "Add Admin Task",
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 400,
                                      child: BlocProvider(
                                        create:
                                            (context) => AdminTaskFormCubit(
                                              context
                                                  .read<AdminTaskCubit>()
                                                  .adminTaskRepo,
                                            ),
                                        child: AdminTaskForm(
                                          editAdminTask: null,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            color: AppColors.secondary,
                          ),
                        ],
                      ),
                      SizedBox(height: 30),
                      Container(
                        padding: EdgeInsets.symmetric(
                          vertical: 30,
                          horizontal: 10,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 15,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 30,
                              ),
                              child: Row(
                                children: [
                                  filterTabs(context, state),
                                  Spacer(),

                                  IgnorePointer(
                                    ignoring:
                                        state.selectedType !=
                                        AdminTaskTypes.completed,
                                    child: Opacity(
                                      opacity:
                                          state.selectedType ==
                                                  AdminTaskTypes.completed
                                              ? 1
                                              : 0,
                                      child: Container(
                                        width: 200,
                                        child: DropdownButtonHideUnderline(
                                          child: DropdownButtonFormField<int>(
                                            decoration: InputDecoration(
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: AppColors.grey2,
                                                ),
                                              ),
                                              hintText: "Select Month",
                                              hintStyle: AppTextStyles.hintText,

                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                            ),
                                            value: state.selectedMonth,
                                            onChanged: (value) {
                                              if (value != null) {
                                                context
                                                    .read<AdminTaskCubit>()
                                                    .selectMonth(value);
                                                context
                                                    .read<AdminTaskCubit>()
                                                    .fetchCompletedAdminTask(
                                                      DateTime(
                                                        DateTime.now().year,
                                                        value,
                                                      ),
                                                    );
                                              }
                                            },
                                            items: List.generate(monthCount, (
                                              index,
                                            ) {
                                              // Wrap around year if needed
                                              final monthIndex =
                                                  ((startMonth + index - 1) %
                                                      12) +
                                                  1;
                                              return DropdownMenuItem<int>(
                                                value: monthIndex,
                                                child: Text(
                                                  monthMap[monthIndex] ?? '',
                                                ),
                                              );
                                            }),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            AdminTaskTile(isMobile: false),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Row filterTabs(BuildContext context, AdminTaskState state) {
    return Row(
      children: [
        FilterContainer(
          title: "Ongoing",
          onFilterTap: () {
            context.read<AdminTaskCubit>().filterTask(
              AdminTaskTypes.onGoing,
              context,
            );
          },
          isSelected: state.selectedType == AdminTaskTypes.onGoing,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Completed",
          onFilterTap: () {
            context.read<AdminTaskCubit>().filterTask(
              AdminTaskTypes.completed,
              context,
            );
          },
          isSelected: state.selectedType == AdminTaskTypes.completed,
        ),
        SizedBox(width: 10),
      ],
    );
  }
}
