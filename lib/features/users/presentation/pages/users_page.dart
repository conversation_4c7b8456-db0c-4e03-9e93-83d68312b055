import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/auth/presentation/pages/registratio_desk.dart';
import 'package:cp_associates/features/home/<USER>/cubit/setting_cubit.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:cp_associates/features/users/presentation/widgets/user_tile_desk.dart';
import 'package:cp_associates/features/users/presentation/widgets/user_tile_mob.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  @override
  void initState() {
    // context.read<AttendanceAdminCubit>().fetchAllUserToadyPunchesRecord();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserCubit, UserState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      child: ResponsiveWid(
        mobile: Scaffold(
          appBar: AppBar(title: Text("Users")),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            // padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      UserTileMob(),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Container(
                            alignment: Alignment.bottomRight,
                            child: AddBtn(
                              text: "Add User",

                              onPressed: () {
                                context.push(Routes.registration);
                              },
                              color: AppColors.secondary,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 30),
              ],
            ),
          ),
        ),
        desktop: Scaffold(
          backgroundColor: AppColors.containerGreyColor,
          appBar: PreferredSize(
            child: CommonAppBar(),
            preferredSize: Size.fromHeight(60),
          ),
          body: GestureDetector(
            onTap: () {
              final settingCubit = context.read<SettingCubit>();
              if (settingCubit.controller.isShowing) {
                settingCubit.controller.toggle();
              } else {
                null;
              }
            },
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 160,
                  vertical: 50,
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        InkWell(
                          onTap: () {
                            context.go(Routes.home);
                          },
                          child: Icon(Icons.arrow_back),
                        ),
                        SizedBox(width: 30),
                        Text("Users", style: AppTextStyles.appBarHeading),
                        Spacer(),
                        AddBtn(
                          text: "Add User",
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return Dialog(
                                  child: Container(
                                    width: 400,
                                    child: RegistrationDesk(),
                                  ),
                                );
                              },
                            );
                          },
                          color: AppColors.secondary,
                        ),
                      ],
                    ),
                    SizedBox(height: 30),
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 30,
                        horizontal: 10,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        // border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 15,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    "Name",
                                    style: AppTextStyles.appBarHeading,
                                  ),
                                ),

                                Expanded(
                                  child: Text(
                                    "Email",
                                    style: AppTextStyles.appBarHeading,
                                  ),
                                ),

                                Expanded(
                                  child: Text(
                                    "Role",
                                    style: AppTextStyles.appBarHeading,
                                  ),
                                ),

                                Expanded(
                                  child: Text(
                                    "Action",
                                    style: AppTextStyles.appBarHeading,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Divider(),
                          UserTileDesk(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
